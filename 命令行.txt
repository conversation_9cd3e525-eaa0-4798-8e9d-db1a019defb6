
#10天 隔日模型 v1
python trade_o.py --weight_type best_ric --save_all_predictions --output_dir gp/cyb --top_n 2 --run_id v1 --start_date 2025-07-01 --end_date 2025-07-03 --data_path tushare_data_cyb/stock_factors_cyb.csv


python trade_o.py --weight_type best_ric --save_all_predictions --output_dir gp/cybF1 --top_n 2 --run_id V1_f1 --start_date 2025-06-01 --end_date 2025-06-27 --data_path tushare_data_cyb/stock_factors_cyb.csv




