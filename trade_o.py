import os
import argparse
import torch
import pandas as pd
import numpy as np
from datetime import datetime, timedelta
import pickle
import json
from tqdm import tqdm
import matplotlib.pyplot as plt

# 导入自定义模块
import config
from data_loader import DataProcessor
from models.samba_model import SAMBAModel
plt.rcParams['font.sans-serif'] = ['SimHei']  # 使用黑体
plt.rcParams['axes.unicode_minus'] = False  # 解决负号显示问题

def parse_args():
    """解析命令行参数"""
    parser = argparse.ArgumentParser(description='SAMBA模型批量交易推荐')
    parser.add_argument('--run_id', type=str, required=True, help='使用的模型运行ID')
    parser.add_argument('--data_path', type=str, required=True, help='输入的CSV数据文件路径')
    parser.add_argument('--start_date', type=str, help='开始日期(YYYY-MM-DD)，不指定则使用数据中的最早日期')
    parser.add_argument('--end_date', type=str, help='结束日期(YYYY-MM-DD)，不指定则使用数据中的最新日期')
    parser.add_argument('--top_n', type=int, default=10, help='推荐的可转债数量')
    parser.add_argument('--output_dir', type=str, default='trade_recommendations', help='输出目录')
    parser.add_argument('--weight_type', type=str, default='best_ric', choices=['best', 'best_ric', 'best_ic'],
                        help='使用的权重类型: best(基于验证损失的最佳模型), best_ric(基于RIC的最佳模型) 或 best_ic(基于IC的最佳模型)')
    parser.add_argument('--batch_size', type=int, default=128, help='预测时的批处理大小')
    parser.add_argument('--save_all_predictions', action='store_true', help='是否保存所有推理结果到CSV文件')
    return parser.parse_args()

def load_model_and_config(run_id, weight_type='best'):
    """
    加载模型和配置

    参数:
        run_id: 训练运行ID
        weight_type: 权重类型, 'best'(默认, 基于验证损失), 'best_ric'(基于RIC) 或 'best_ic'(基于IC)
    """
    # 加载运行配置
    run_dir = os.path.join(config.OUTPUT_DIR, run_id)
    if not os.path.exists(run_dir):
        raise ValueError(f"找不到指定的运行ID目录: {run_dir}")
    
    with open(os.path.join(run_dir, 'config.json'), 'r') as f:
        run_config = json.load(f)
    
    with open(os.path.join(run_dir, 'data_info.pkl'), 'rb') as f:
        data_info = pickle.load(f)
    
    # 加载模型
    model = SAMBAModel(
        input_dim=len(run_config['factors']),
        d_model=run_config['d_model'],
        n_layer=run_config['n_layer'],
        num_heads=run_config['num_heads'],
        gnn_k=run_config['gnn_k'],
        node_embedding_dim=run_config['node_embedding_dim'],
        cnn_blocks=run_config['cnn_blocks'],
        cnn_kernel_sizes=run_config['cnn_kernel_sizes'],
        cnn_bottleneck_scale= run_config['cnn_bottleneck_scale'],
        dropout=run_config['dropout'],
        lookback_window=run_config['lookback_window']  # 使用训练时的lookback_window
    ).to(config.DEVICE)
    
    # 根据权重类型选择模型文件名
    if weight_type == 'best':
        model_filename = 'best_model.pt'
    elif weight_type == 'best_ric':
        model_filename = 'best_ric_model.pt'
    elif weight_type == 'best_ic':
        model_filename = 'best_ic_model.pt'
    else:
        raise ValueError(f"不支持的权重类型: {weight_type}")

    best_model_path = os.path.join(run_dir, model_filename)
    
    if not os.path.exists(best_model_path):
        raise ValueError(f"找不到{weight_type}模型权重: {best_model_path}")
    
    print(f"使用{weight_type}模型权重: {best_model_path}")
    checkpoint = torch.load(best_model_path, map_location=config.DEVICE, weights_only=False)
    model.load_state_dict(checkpoint['model_state_dict'])
    model.eval()
    
    return model, run_config, data_info, run_dir

def prepare_batch_data(df, target_dates, run_config, data_processor):
    """一次性准备所有日期的数据，确保有足够的历史"""
    # 初始化存储所有日期数据的字典
    all_features = {}
    all_date_codes = {}
    
    print("一次性准备所有目标日期的数据...")
    
    # 一次性处理所有可转债
    all_codes = df['ts_code'].unique()
    
    # 初始化每个目标日期的数据结构
    for date in target_dates:
        all_features[date] = {}
        all_date_codes[date] = []
    
    # 跟踪统计数据
    total_success = 0
    total_insufficient_history = 0
    
    for code in tqdm(all_codes, desc="处理所有可转债"):
        # 获取该可转债的所有历史数据（一次性获取）
        df_code = df[df['ts_code'] == code].sort_values('trade_date')
        
        # 确保所有因子都存在
        skip_code = False
        for factor in run_config['factors']:
            if factor not in df_code.columns:
                print(f"因子 {factor} 不在数据中，跳过 {code}")
                skip_code = True
                break
                
        if skip_code:
            continue
        
        # 针对每个目标日期生成特征
        for date in target_dates:
            # 找到目标日期在历史中的位置
            date_data = df_code[df_code['trade_date'] == date]
            if len(date_data) == 0:
                continue
                
            date_idx = date_data.index[0]
            
            # 获取lookback_window长度的历史数据
            df_history = df_code.loc[:date_idx].tail(run_config['lookback_window'])
            
            # 检查是否有足够的历史数据
            if len(df_history) < run_config['lookback_window']:
                total_insufficient_history += 1
                # 在发生多次后，打印一次警告，避免输出过多
                if total_insufficient_history % 100 == 1:
                    print(f"警告: {code} 在 {date.strftime('%Y-%m-%d')} 只有 {len(df_history)} 天历史数据，需要 {run_config['lookback_window']} 天")
                continue
            
            try:
                # 提取特征
                features = df_history[run_config['factors']].values
                
                if features.shape[1] != data_processor.scaler.n_features_in_:
                    print(f"特征数不匹配: {code} 有 {features.shape[1]} 个特征，标准化器期望 {data_processor.scaler.n_features_in_} 个特征")
                    continue
                
                # 标准化特征
                features_scaled = data_processor._apply_scaling(features.reshape(1, *features.shape))
                
                # 保存该日期的该可转债特征
                all_features[date][code] = features_scaled
                all_date_codes[date].append(code)
                
                total_success += 1
                
            except Exception as e:
                print(f"处理 {code} 在 {date.strftime('%Y-%m-%d')} 时出错: {str(e)}")
                continue
    
    print(f"成功处理 {total_success} 个样本，{total_insufficient_history} 个样本因历史数据不足被跳过")
    
    return all_features, all_date_codes

def batch_generate_predictions(model, all_features, batch_size=64):
    """批量生成预测结果"""
    all_predictions = {}
    
    print("使用批处理方式进行模型预测...")
    
    with torch.no_grad():
        for date, features_dict in tqdm(all_features.items(), desc="批量预测"):
            # 如果当前日期没有足够的数据，跳过
            if not features_dict:
                all_predictions[date] = {}
                continue
            
            # 收集当前日期所有可转债代码和特征
            codes = list(features_dict.keys())
            features_list = [features_dict[code] for code in codes]
            
            # 如果特征为空，跳过
            if not features_list:
                all_predictions[date] = {}
                continue
                
            # 将特征堆叠为批次
            features_tensor = torch.FloatTensor(np.vstack(features_list)).to(config.DEVICE)
            
            # 使用batch_size批量预测
            predictions = {}
            for i in range(0, len(codes), batch_size):
                batch_codes = codes[i:i+batch_size]
                batch_features = features_tensor[i:i+batch_size]
                
                # 模型预测
                batch_preds = model(batch_features).cpu().numpy()
                
                # 存储预测结果
                for j, code in enumerate(batch_codes):
                    predictions[code] = batch_preds[j]
            
            # 存储当前日期的所有预测
            all_predictions[date] = predictions
    
    return all_predictions

def calculate_prediction_changes(all_predictions, target_dates, actual_target_dates):
    """计算预测值变化"""
    print("计算预测值变化...")
    
    # 创建预测值变化字典
    prediction_changes = {}
    
    # 将日期转换为可以排序的格式
    sorted_all_dates = sorted(target_dates)
    
    for current_date in actual_target_dates:
        # 找到前一个交易日
        current_idx = sorted_all_dates.index(current_date)
        if current_idx > 0:
            prev_date = sorted_all_dates[current_idx - 1]
            
            # 获取当前日期和前一日期的预测
            current_predictions = all_predictions.get(current_date, {})
            prev_predictions = all_predictions.get(prev_date, {})
            
            # 计算变化
            changes = {}
            for code in current_predictions.keys():
                if code in prev_predictions:
                    changes[code] = current_predictions[code] - prev_predictions[code]
                else:
                    changes[code] = np.nan  # 如果前一天没有预测值，设为NaN
            
            prediction_changes[current_date] = changes
        else:
            # 第一个日期没有前一天的数据
            prediction_changes[current_date] = {}
    
    return prediction_changes

def save_all_predictions_to_csv(all_predictions, prediction_changes, df, actual_target_dates, args, output_dir):
    """
    保存所有推理结果到CSV文件

    参数:
        all_predictions: 所有预测结果字典 {date: {code: prediction_value}}
        prediction_changes: 预测值变化字典 {date: {code: change_value}}
        df: 原始数据DataFrame
        actual_target_dates: 实际目标日期列表
        args: 命令行参数
        output_dir: 输出目录
    """
    print("正在保存所有推理结果到CSV文件...")

    all_results = []

    for date in tqdm(actual_target_dates, desc="整理所有推理结果"):
        # 跳过无数据的日期
        if date not in all_predictions or not all_predictions[date]:
            continue

        # 获取该日期的预测结果和变化
        predictions = all_predictions[date]
        changes = prediction_changes.get(date, {})

        if not predictions:
            continue

        # 获取该日期的原始数据
        df_date = df[df['trade_date'] == date]

        # 为每个股票代码创建一行记录
        for code, pred_value in predictions.items():
            # 获取该股票的基本信息
            stock_data = df_date[df_date['ts_code'] == code]

            if len(stock_data) > 0:
                stock_info = stock_data.iloc[0]

                result_row = {
                    'trade_date': date,
                    'ts_code': code,
                    'predicted_return': pred_value[0] if isinstance(pred_value, np.ndarray) else pred_value,
                    'prediction_change': changes.get(code, np.nan),
                    'actual_pct_chg': stock_info.get('pct_chg', np.nan),
                    'model_run_id': args.run_id,
                    'weight_type': args.weight_type
                }

                all_results.append(result_row)

    if all_results:
        # 创建DataFrame
        results_df = pd.DataFrame(all_results)

        # 按日期和预测值排序
        results_df = results_df.sort_values(['trade_date', 'predicted_return'], ascending=[True, False])

        # 保存到CSV文件
        all_predictions_path = os.path.join(output_dir, f"all_predictions_{args.weight_type}.csv")
        results_df.to_csv(all_predictions_path, index=False)

        print(f"已保存所有推理结果至 {all_predictions_path}")
        print(f"总共保存了 {len(results_df)} 条推理记录，涵盖 {len(actual_target_dates)} 个交易日")

        # 打印一些统计信息
        print(f"预测值范围: {results_df['predicted_return'].min():.6f} 到 {results_df['predicted_return'].max():.6f}")
        print(f"平均预测值: {results_df['predicted_return'].mean():.6f}")

        return all_predictions_path
    else:
        print("没有推理结果可保存")
        return None

def batch_generate_recommendations(args):
    """批量生成交易推荐"""
    # 创建输出目录
    weight_type_str = args.weight_type
    output_dir = f"{args.output_dir}_{weight_type_str}"
    os.makedirs(output_dir, exist_ok=True)
    
    # 一次性加载所有数据
    print(f"正在一次性加载所有数据: {args.data_path}")
    df = pd.read_csv(args.data_path)
    df['trade_date'] = pd.to_datetime(df['trade_date'])

    # 设置日期范围
    start_date = pd.to_datetime(args.start_date) if args.start_date else df['trade_date'].min()
    end_date = pd.to_datetime(args.end_date) if args.end_date else df['trade_date'].max()
    
    # 找出实际的目标预测日期范围内的交易日
    actual_target_dates = df[(df['trade_date'] >= start_date) & (df['trade_date'] <= end_date)]['trade_date'].unique()
    actual_target_dates = sorted(actual_target_dates)
    
    if not actual_target_dates:
        raise ValueError(f"在{start_date.strftime('%Y-%m-%d')}至{end_date.strftime('%Y-%m-%d')}范围内没有找到交易日数据")
    
    # 为了计算预测值变化，需要往前多加载一个交易日
    # 找到start_date之前的一个交易日
    all_trading_dates = sorted(df['trade_date'].unique())
    start_idx = None
    for i, date in enumerate(all_trading_dates):
        if date >= start_date:
            start_idx = i
            break
    
    # 如果能找到前一个交易日，就包含它
    extended_start_date = start_date
    if start_idx is not None and start_idx > 0:
        extended_start_date = all_trading_dates[start_idx - 1]
        print(f"为计算预测值变化，往前扩展一个交易日: {extended_start_date.strftime('%Y-%m-%d')}")
    
    # 计算往前多加载的日期，确保有足够历史数据
    lookback_days = run_config['lookbook_window'] if 'run_config' in locals() else 20  # 先设个默认值
    final_extended_start_date = extended_start_date - pd.Timedelta(days=lookback_days*2)
    
    print(f"实际目标日期范围: {start_date.strftime('%Y-%m-%d')} 至 {end_date.strftime('%Y-%m-%d')}")
    print(f"预测计算日期范围: {extended_start_date.strftime('%Y-%m-%d')} 至 {end_date.strftime('%Y-%m-%d')}")
    print(f"数据加载日期范围: {final_extended_start_date.strftime('%Y-%m-%d')} 至 {end_date.strftime('%Y-%m-%d')}")
        
    # 加载模型和配置
    print(f"正在加载模型和配置...使用{args.weight_type}权重模型")
    model, run_config, _, run_dir = load_model_and_config(args.run_id, args.weight_type)
    
    # 重新计算final_extended_start_date，现在我们有了lookback_window
    lookback_days = run_config['lookback_window'] + 6
    final_extended_start_date = extended_start_date - pd.Timedelta(days=lookback_days*2)
    
    # 预先筛选扩展日期范围内的数据
    df = df[(df['trade_date'] >= final_extended_start_date) & (df['trade_date'] <= end_date)]
    
    # 找出所有需要预测的日期（包含前一个交易日）
    target_dates = df[(df['trade_date'] >= extended_start_date) & (df['trade_date'] <= end_date)]['trade_date'].unique()
    target_dates = sorted(target_dates)
    
    print(f"总共需要预测{len(target_dates)}个交易日，实际输出{len(actual_target_dates)}个交易日")
    
    # 加载数据处理器
    data_processor = DataProcessor(
        factors=run_config['factors'],
        lookback=run_config['lookback_window'],
        scaler_path=os.path.join(run_dir, 'feature_scaler.pkl')
    )
    
    # 一次性准备目标日期的数据
    all_features, _ = prepare_batch_data(
        df, target_dates, run_config, data_processor
    )
    
    # 批量生成预测
    all_predictions = batch_generate_predictions(model, all_features, args.batch_size)
    
    # 计算预测值变化
    prediction_changes = calculate_prediction_changes(all_predictions, target_dates, actual_target_dates)

    # 保存所有推理结果到CSV文件（如果启用）
    if args.save_all_predictions:
        save_all_predictions_to_csv(all_predictions, prediction_changes, df, actual_target_dates, args, output_dir)

    # 直接创建结果DataFrame列表
    result_dfs = []
    change_result_dfs = []
    
    # 对每个实际目标日期处理结果
    for date in tqdm(actual_target_dates, desc="处理预测结果"):
        try:
            # 跳过无数据的日期
            if date not in all_predictions or not all_predictions[date]:
                print(f"日期 {date.strftime('%Y-%m-%d')} 没有预测结果")
                continue
            
            # 获取该日期的预测结果
            predictions = all_predictions[date]
            changes = prediction_changes.get(date, {})
            
            if not predictions:
                continue
            
            # 创建结果DataFrame
            df_date = df[df['trade_date'] == date]
            results = pd.DataFrame({
                'ts_code': list(predictions.keys()),
                'predicted_return': list(predictions.values())
            })
            
            # 添加预测值变化
            results['prediction_change'] = results['ts_code'].map(changes)
            
            # 添加基本信息
            results = results.merge(
                df_date[['ts_code', 'pct_chg']],
                on='ts_code',
                how='left'
            )
            
            # 按预测回报排序（原始排序）
            results = results.sort_values('predicted_return', ascending=False).reset_index(drop=True)
            
            # 取前N个
            top_results = results.head(args.top_n).copy()
            top_results['prediction_date'] = date
            top_results['model_run_id'] = args.run_id
            top_results['weight_type'] = args.weight_type
            top_results['rank'] = range(1, len(top_results) + 1)
            
            # 重排列的列顺序
            top_results = top_results[['rank', 'ts_code', 'predicted_return', 'prediction_change',
                                     'pct_chg','prediction_date','model_run_id', 'weight_type']]
            
            # 添加到结果列表
            result_dfs.append(top_results)
            
            # 创建按预测值变化排序的结果
            if not changes or all(pd.isna(v) for v in changes.values()):
                print(f"日期 {date.strftime('%Y-%m-%d')} 没有预测值变化数据")
            else:
                # 过滤掉NaN值
                results_with_changes = results.dropna(subset=['prediction_change'])
                
                if len(results_with_changes) > 0:
                    # 按预测值变化排序
                    change_results = results_with_changes.sort_values('prediction_change', ascending=False).reset_index(drop=True)
                    
                    # 取前N个
                    top_change_results = change_results.head(args.top_n).copy()
                    top_change_results['prediction_date'] = date
                    top_change_results['model_run_id'] = args.run_id
                    top_change_results['weight_type'] = args.weight_type
                    top_change_results['rank'] = range(1, len(top_change_results) + 1)
                    
                    # 重排列的列顺序
                    top_change_results = top_change_results[['rank', 'ts_code', 'predicted_return', 'prediction_change',
                                                           'pct_chg','prediction_date','model_run_id', 'weight_type']]
                    
                    # 添加到变化结果列表
                    change_result_dfs.append(top_change_results)
            
        except Exception as e:
            print(f"处理日期 {date.strftime('%Y-%m-%d')} 结果时出错: {str(e)}")
            continue
    
    # 一次性合并所有结果
    if result_dfs:
        all_df = pd.concat(result_dfs, ignore_index=True)
        
        # 保存汇总结果（按预测值排序）
        summary_path = os.path.join(output_dir, f"all_recommendations_{args.weight_type}.csv")
        all_df.to_csv(summary_path, index=False)
        print(f"\n已保存所有推荐结果（按预测值排序）至 {summary_path}")
    else:
        print("没有生成任何推荐（按预测值排序）")

    # 保存按预测值变化排序的结果
    if change_result_dfs:
        all_change_df = pd.concat(change_result_dfs, ignore_index=True)
        
        # 保存汇总结果（按预测值变化排序）
        change_summary_path = os.path.join(output_dir, f"all_recommendations_by_change_{args.weight_type}.csv")
        all_change_df.to_csv(change_summary_path, index=False)
        print(f"已保存所有推荐结果（按预测值变化排序）至 {change_summary_path}")
    else:
        print("没有生成任何推荐（按预测值变化排序）")

if __name__ == "__main__":
    args = parse_args()
    batch_generate_recommendations(args)